from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional
import pandas as pd


class ModelRepositoryInterface(ABC):
    """
    Interface for repositories managing output of pricing and risk model, i.e. expected returns and covariance matrix for asset listings.
    Implementations should provide methods for saving and retrieving expected returns and covariance matrices.
    """

    @abstractmethod
    def save_expected_returns(
            self,
            expected_returns: pd.DataFrame,
            date: Optional[datetime] = None
    ) -> None:
        """
        Save expected returns for a given date, using asset_listing_code as identifier.
        Overwrites any existing records for that date.

        Args:
            expected_returns (pd.DataFrame): DataFrame with expected returns, indexed or containing 'asset_listing_code' and 'expected_return' columns.
            date (Optional[datetime]): The date for which expected returns are saved. Defaults to now if not provided.

        Returns:
            None
        """
        pass

    @abstractmethod
    def get_expected_returns_for_asset_listings(
            self,
            asset_listing_codes: Optional[List[str]] = None,
            date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Fetch the latest expected return for each asset_listing_code up to a given date.

        Args:
            asset_listing_codes (Optional[List[str]]): List of asset listing codes to filter. If None, fetch for all.
            date (Optional[datetime]): The latest date to consider for expected returns. Defaults to now if not provided.

        Returns:
            pd.DataFrame: DataFrame with columns ['asset_listing_code', 'expected_return'].
        """
        pass

    @abstractmethod
    def save_covariance(
            self,
            covariance_df: pd.DataFrame,
            date: Optional[datetime] = None
    ) -> None:
        """
        Save covariance matrix for a given date using asset_listing_code.
        Only upper triangle (including diagonal) is stored.
        Overwrites any existing records for that date.

        Args:
            covariance_df (pd.DataFrame): Symmetric DataFrame representing the covariance matrix, indexed and columned by asset_listing_code.
            date (Optional[datetime]): The date for which the covariance matrix is saved. Defaults to now if not provided.

        Returns:
            None
        """
        pass

    @abstractmethod
    def get_covariance_for_asset_listings(
            self,
            asset_listing_codes: Optional[List[str]] = None,
            date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Fetch the latest covariance submatrix for a list of asset_listing_codes up to a given date.

        Args:
            asset_listing_codes (Optional[List[str]]): List of asset listing codes to filter. If None, fetch for all.
            date (Optional[datetime]): The latest date to consider for covariances. Defaults to now if not provided.

        Returns:
            pd.DataFrame: Symmetric DataFrame with asset_listing_codes as both index and columns, containing covariance values.
        """
        pass