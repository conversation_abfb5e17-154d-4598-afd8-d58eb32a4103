from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional
import pandas as pd

class EtfRepositoryInterface(ABC):
    """
    Interface for ETF repositories.
    Implementations should handle ETF data retrieval and storage operations.
    """

    @abstractmethod
    def get_latest_prices_eur(
            self,
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Get the latest prices in EUR for ETFs.

        Args:
            last_date: Optional date to get prices up to

        Returns:
            DataFrame with prices
        """
        pass

    @abstractmethod
    def get_latest_prices_eur_for_asset_listing_codes(
            self,
            asset_listing_codes: List[str],
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Retrieve the latest EUR prices for the given asset listing codes up to the specified date.

        Args:
            asset_listing_codes (List[str]): List of asset listing codes.
            last_date (Optional[datetime]): The last date to consider for prices.

        Returns:
            pd.DataFrame: A DataFrame with dates as index and asset listing codes as columns.
        """
        pass

    @abstractmethod
    def get_returns_eur_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Get returns for ETFs within a specified time horizon.

        Args:
            time_horizon_in_years: Number of years to look back
            last_date: Optional end date for the time horizon

        Returns:
            DataFrame with returns
        """
        pass

    @abstractmethod
    def save_simulated_returns(
            self,
            simulated_returns: pd.DataFrame
    ) -> None:
        """
        Save simulated returns for ETFs.

        Args:
            simulated_returns: DataFrame with simulated returns
        """
        pass

    @abstractmethod
    def get_simulations_for_asset_listing_codes(
            self,
            asset_listing_codes: List[str],
            n_simulations: Optional[int] = None
    ) -> pd.DataFrame:
        """
        Retrieve simulation results for the given asset listing codes.

        Args:
            asset_listing_codes (List[str]): List of asset listing codes.
            n_simulations (Optional[int]): Number of simulations to retrieve.

        Returns:
            pd.DataFrame: Simulation results as a DataFrame.
        """
        pass