from abc import ABC, abstractmethod
from typing import Optional, List, Dict

import pandas as pd
import numpy as np
from datetime import datetime

from sklearn.linear_model import LinearRegression

from configuration.config import Settings
from repositories.interfaces.asset_class_repository_interface import AssetClassRepositoryInterface
from repositories.interfaces.etf_repository_interface import EtfRepositoryInterface
from repositories.interfaces.metadata_repository_interface import MetadataRepositoryInterface
from repositories.interfaces.risk_free_rate_repository_interface import RiskFreeRateRepositoryInterface
from utils import util


class PricingModel(ABC):

    def __init__(
            self,
            etf_repository: EtfRepositoryInterface,
            metadata_repository: MetadataRepositoryInterface,
            risk_free_rate_repository: RiskFreeRateRepositoryInterface,
            asset_class_repository: AssetClassRepositoryInterface,
            settings: Settings
    ):
        self.settings = settings
        self.etf_repository = etf_repository
        self.metadata_repository = metadata_repository
        self.risk_free_rate_repository = risk_free_rate_repository
        self.asset_class_repository = asset_class_repository

    @abstractmethod
    def calculate_products_returns_and_covariance(
            self,
            avg_risk_free_rate: float,
            current_risk_free_rate: float,
            last_date: datetime
    ) -> (pd.DataFrame, pd.DataFrame):
        pass


class RegressionPricingModel(PricingModel):

    def __init__(
            self,
            etf_repository: EtfRepositoryInterface,
            metadata_repository: MetadataRepositoryInterface,
            risk_free_rate_repository: RiskFreeRateRepositoryInterface,
            asset_class_repository: AssetClassRepositoryInterface,
            settings: Settings
    ):
        super().__init__(
            etf_repository,
            metadata_repository,
            risk_free_rate_repository,
            asset_class_repository,
            settings
        )

    def calculate_products_returns_and_covariance(
            self,
            avg_risk_free_rate: float,
            current_risk_free_rate: float,
            last_date: Optional[datetime] = None
    ) -> (pd.DataFrame, pd.DataFrame):
        if last_date is None:
            last_date = datetime.today()

        asset_class_returns, last_document_date = self.asset_class_repository.get_returns_in_time_horizon(
            self.settings.time_horizon, last_date
        )
        asset_class_market_caps = self.asset_class_repository.get_market_caps(last_date)
        asset_class_weights = self.__calculate_weights(asset_class_market_caps)

        risk_free_rates, last_document_date = self.risk_free_rate_repository.get_risk_free_rates_in_time_horizon(
            self.settings.time_horizon, last_document_date
        )

        etf_returns = self.etf_repository.get_returns_eur_in_time_horizon(
            self.settings.time_horizon, last_document_date
        )

        ter_mapping = self.metadata_repository.get_ter_for_all_listings()

        asset_class_returns, risk_free_rates, etf_returns = util.synchronize_and_fill_dates(
            [asset_class_returns, risk_free_rates, etf_returns]
        )

        market_portfolio_returns = util.calculate_market_portfolio_returns(
            asset_class_returns, asset_class_weights
        )
        risk_free_rates_daily = util.get_daily_return(risk_free_rates)

        betas = RegressionPricingModel.__calculate_betas(etf_returns, market_portfolio_returns, risk_free_rates_daily)

        # arithmetic average excess return of GMP from paper "Historical Returns of the Market Portfolio"
        expected_market_portfolio_excess_return = util.get_daily_return(0.0398)
        expected_products_excess_returns_daily = betas * expected_market_portfolio_excess_return
        expected_products_excess_returns = util.get_annual_return(
            expected_products_excess_returns_daily.to_numpy()
        ).reshape(-1, 1)

        expected_products_returns = RegressionPricingModel.__apply_ter_to_returns(
            expected_products_excess_returns,
            list(etf_returns.columns),
            ter_mapping,
            current_risk_free_rate
        )

        etf_cov = util.get_annual_covariance(etf_returns)

        columns = list(etf_returns.columns)


        return (
            pd.DataFrame({
                'asset_listing_code': columns,
                'expected_return': expected_products_returns.flatten()
            }),
            pd.DataFrame(etf_cov, columns=columns, index=columns)
        )

    @staticmethod
    def __calculate_weights(market_caps: pd.DataFrame) -> np.ndarray:
        total_sum = market_caps.sum(axis=1)
        weights = market_caps.apply(lambda x: x / total_sum)
        return weights.to_numpy()

    @staticmethod
    def __calculate_beta(product_returns: np.ndarray, market_portfolio_returns: np.ndarray, risk_free_rates: np.ndarray) -> float:
        product_returns, market_portfolio_returns, risk_free_rates = product_returns.reshape(-1, 1), market_portfolio_returns.reshape(-1, 1), risk_free_rates.reshape(-1, 1)
        regression = LinearRegression(fit_intercept=True)
        regression.fit(market_portfolio_returns - risk_free_rates, product_returns - risk_free_rates)
        return regression.coef_.item()

    @staticmethod
    def __calculate_betas(etf_returns: pd.DataFrame, market_portfolio_returns: np.ndarray, risk_free_rates: pd.DataFrame) -> pd.DataFrame:
        betas = {}
        for product in etf_returns.columns:
            betas[product] = RegressionPricingModel.__calculate_beta(etf_returns[product].values, market_portfolio_returns, risk_free_rates.values)
        return pd.DataFrame(betas, index=['beta'])

    @staticmethod
    def __apply_ter_to_returns(
            expected_returns: np.ndarray,
            listing_codes: List[str],
            ter_mapping: Dict[str, float],
            current_risk_free_rate: float
    ) -> np.ndarray:
        ter_values = np.array([ter_mapping.get(code, 0) / 100 for code in listing_codes])

        returns_with_ter = expected_returns - ter_values.reshape(-1, 1)

        returns_with_risk_free = returns_with_ter + current_risk_free_rate

        return returns_with_risk_free
